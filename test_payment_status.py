#!/usr/bin/env python3
"""
Test script for payment status checking functionality
"""
import requests
import json
import time

def test_payment_status_endpoint():
    """Test the payment status checking endpoint"""
    print("🧪 Testing Payment Status Checking System")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    
    # Test 1: Check endpoint without authentication (should fail)
    print("\n1. Testing endpoint without authentication...")
    try:
        response = requests.get(f"{base_url}/student/api/check-payment-status", timeout=5)
        print(f"   Status Code: {response.status_code}")
        if response.status_code == 302:
            print("   ✅ Correctly redirected (authentication required)")
        else:
            print(f"   Response: {response.text[:200]}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: Test MoMo API directly
    print("\n2. Testing MoMo API directly...")
    try:
        from services.momo_api import momo_api
        
        # Test payment initiation
        print("   Testing payment initiation...")
        payment_result = momo_api.request_to_pay("0788123456", 100)
        print(f"   Payment result: {payment_result}")
        
        if payment_result and payment_result.get('success'):
            transaction_id = payment_result.get('transaction_id')
            print(f"   ✅ Payment initiated. Transaction ID: {transaction_id}")
            
            # Test payment status check
            print("   Testing payment status check...")
            status_result = momo_api.check_payment_status(transaction_id)
            print(f"   Status result: {status_result}")
            
            if status_result and status_result.get('success'):
                print(f"   ✅ Status check successful. Status: {status_result.get('status')}")
            else:
                print(f"   ❌ Status check failed: {status_result}")
        else:
            print(f"   ❌ Payment initiation failed: {payment_result}")
            
    except Exception as e:
        print(f"   ❌ Error testing MoMo API: {e}")
        import traceback
        print(f"   Traceback: {traceback.format_exc()}")
    
    # Test 3: Test database functions
    print("\n3. Testing database functions...")
    try:
        from simple_database_service import update_payment_status_for_request, add_payment
        
        print("   Testing update_payment_status_for_request...")
        # This will fail because request ID 999 doesn't exist, but it tests the function
        result = update_payment_status_for_request(999, 'paid', 'TEST_TRANSACTION_123')
        print(f"   Result (expected False): {result}")
        
        print("   Testing add_payment...")
        # This will also fail because student doesn't exist, but it tests the function
        result = add_payment('TEST_STUDENT', '2023-2024', 100, 'mobile_money', 999, 'Test Department')
        print(f"   Result (expected False): {result}")
        
        print("   ✅ Database functions are callable")
        
    except Exception as e:
        print(f"   ❌ Error testing database functions: {e}")
        import traceback
        print(f"   Traceback: {traceback.format_exc()}")
    
    print("\n" + "=" * 50)
    print("🏁 Payment Status Test Complete")

if __name__ == "__main__":
    test_payment_status_endpoint()
