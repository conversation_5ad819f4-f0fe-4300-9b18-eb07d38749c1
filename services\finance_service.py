"""
Finance service layer for INES Transcript System
Business logic for finance operations
"""
from simple_database_service import (
    get_finance_dashboard_data as get_dashboard_data,
    approve_transcript_request,
    reject_transcript_request,
    delete_transcript_request as delete_request_permanently
)

def get_finance_dashboard_data():
    """Get finance dashboard data"""
    return get_dashboard_data()

def approve_request(request_id, finance_user_id):
    """Approve a transcript request"""
    try:
        return approve_transcript_request(request_id, finance_user_id)
    except Exception as e:
        print(f"Error approving request {request_id}: {e}")
        return False

def reject_request(request_id, finance_user_id, rejection_reason=None):
    """Reject a transcript request"""
    try:
        return reject_transcript_request(request_id, finance_user_id, rejection_reason)
    except Exception as e:
        print(f"Error rejecting request {request_id}: {e}")
        return False

def delete_request(request_id):
    """Delete a processed request"""
    try:
        return delete_request_permanently(request_id)
    except Exception as e:
        print(f"Error deleting request {request_id}: {e}")
        return False