<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Status Test - INES Transcript System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Payment Status Testing Tool</h1>
        <p>This tool helps test the payment status checking functionality.</p>
        
        <div class="test-section info">
            <h3>📋 Test Instructions</h3>
            <ol>
                <li>Click "Simulate Payment Session" to create a mock payment session</li>
                <li>Click "Check Payment Status" to test the status checking endpoint</li>
                <li>Watch the logs to see how the system responds</li>
                <li>After 30 seconds, the demo mode will auto-approve the payment</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🎮 Test Controls</h3>
            <button onclick="simulatePaymentSession()">Simulate Payment Session</button>
            <button onclick="checkPaymentStatus()">Check Payment Status</button>
            <button onclick="clearLogs()">Clear Logs</button>
            <button onclick="startAutoCheck()">Start Auto-Check (2s interval)</button>
            <button onclick="stopAutoCheck()">Stop Auto-Check</button>
        </div>

        <div class="test-section">
            <h3>📊 Current Status</h3>
            <div id="status">No payment session active</div>
        </div>

        <div class="test-section">
            <h3>📝 Test Logs</h3>
            <div id="logs" class="log">Ready to start testing...\n</div>
        </div>
    </div>

    <script>
        let autoCheckInterval = null;
        let currentTransactionId = null;
        let paymentStartTime = null;

        function log(message) {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            logs.textContent += `[${timestamp}] ${message}\n`;
            logs.scrollTop = logs.scrollHeight;
        }

        function updateStatus(message, className = '') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = className;
        }

        function simulatePaymentSession() {
            // Create a mock payment session
            currentTransactionId = 'TEST_' + Math.random().toString(36).substr(2, 9);
            paymentStartTime = Date.now();
            
            log(`🚀 Simulating payment session with transaction ID: ${currentTransactionId}`);
            updateStatus(`Payment session active - Transaction: ${currentTransactionId}`, 'info');
            
            // Simulate setting session data (this would normally be done by the payment initiation)
            fetch('/test/set-payment-session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    transaction_id: currentTransactionId,
                    phone_number: '250788123456',
                    amount: 100,
                    timestamp: paymentStartTime / 1000
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    log('✅ Payment session created successfully');
                } else {
                    log('❌ Failed to create payment session: ' + data.message);
                }
            })
            .catch(error => {
                log('❌ Error creating payment session: ' + error);
            });
        }

        function checkPaymentStatus() {
            if (!currentTransactionId) {
                log('❌ No payment session active. Please simulate a payment session first.');
                return;
            }

            log(`🔍 Checking payment status for transaction: ${currentTransactionId}`);
            
            fetch('/student/api/check-payment-status', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                log(`📊 Payment status response: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success) {
                    const status = data.status;
                    if (status === 'successful') {
                        updateStatus('✅ Payment completed successfully!', 'success');
                        log('🎉 Payment successful! Database should be updated.');
                        if (autoCheckInterval) {
                            stopAutoCheck();
                        }
                    } else if (status === 'pending') {
                        updateStatus('⏳ Payment pending...', 'warning');
                        log('⏳ Payment still pending. Continue monitoring...');
                    } else if (status === 'failed') {
                        updateStatus('❌ Payment failed', 'error');
                        log('❌ Payment failed.');
                        if (autoCheckInterval) {
                            stopAutoCheck();
                        }
                    }
                } else {
                    updateStatus('❌ Error checking payment status', 'error');
                    log('❌ Error: ' + data.message);
                }
            })
            .catch(error => {
                log('❌ Network error checking payment status: ' + error);
                updateStatus('❌ Network error', 'error');
            });
        }

        function startAutoCheck() {
            if (autoCheckInterval) {
                log('⚠️ Auto-check already running');
                return;
            }
            
            if (!currentTransactionId) {
                log('❌ No payment session active. Please simulate a payment session first.');
                return;
            }

            log('🔄 Starting automatic payment status checking (every 2 seconds)');
            autoCheckInterval = setInterval(checkPaymentStatus, 2000);
        }

        function stopAutoCheck() {
            if (autoCheckInterval) {
                clearInterval(autoCheckInterval);
                autoCheckInterval = null;
                log('⏹️ Stopped automatic payment status checking');
            }
        }

        function clearLogs() {
            document.getElementById('logs').textContent = 'Logs cleared...\n';
        }

        // Show elapsed time
        setInterval(() => {
            if (paymentStartTime) {
                const elapsed = Math.floor((Date.now() - paymentStartTime) / 1000);
                if (elapsed < 30) {
                    const remaining = 30 - elapsed;
                    document.getElementById('status').textContent += ` (Demo auto-approve in ${remaining}s)`;
                }
            }
        }, 1000);
    </script>
</body>
</html>
