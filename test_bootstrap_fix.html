<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bootstrap Loading Test</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Bootstrap Loading Test</h1>
        <div id="bootstrap-status" class="alert alert-info">
            Checking Bootstrap status...
        </div>
        
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#testModal">
            Test Modal
        </button>
        
        <!-- Test Modal -->
        <div class="modal fade" id="testModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Test Modal</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>If you can see this modal, Bootstrap is working correctly!</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Test Bootstrap loading
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                const statusDiv = document.getElementById('bootstrap-status');
                
                if (typeof bootstrap === 'undefined') {
                    statusDiv.className = 'alert alert-danger';
                    statusDiv.innerHTML = '❌ Bootstrap is NOT loaded! Modal functionality will not work.';
                } else {
                    statusDiv.className = 'alert alert-success';
                    statusDiv.innerHTML = '✅ Bootstrap is loaded successfully! Modal functionality should work.';
                    
                    // Test modal programmatically
                    console.log('Bootstrap version:', bootstrap);
                    console.log('Modal class available:', typeof bootstrap.Modal);
                }
            }, 100);
        });
    </script>
</body>
</html>
