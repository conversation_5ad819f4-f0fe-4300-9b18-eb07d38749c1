"""
Student routes for INES Transcript System
Student dashboard, requests, and downloads
"""
from flask import Blueprint, render_template, request, redirect, url_for, session, flash, jsonify
from utils.decorators import login_required, student_required
from datetime import datetime
import time
from services.student_service import (
    get_student_dashboard_data,
    create_transcript_request,
    get_student_requests,
    download_transcript_file
)

student_bp = Blueprint('student', __name__)

@student_bp.route('/dashboard')
@login_required
@student_required
def dashboard():
    """Student dashboard"""
    student_id = session['user_id']
    dashboard_data = get_student_dashboard_data(student_id)
    
    return render_template('student/dashboard.html', **dashboard_data)

@student_bp.route('/request-transcript', methods=['GET', 'POST'])
@login_required
@student_required
def request_transcript():
    """Request transcript form and submission"""
    if request.method == 'POST':
        academic_years = request.form.getlist('academic_years')
        email = request.form.get('email')
        
        if not academic_years:
            flash('Please select at least one academic year', 'error')
            return redirect(url_for('student.request_transcript'))
        
        # Store in session for payment
        session['transcript_request'] = {
            'academic_years': academic_years,
            'email': email,
            'count': len(academic_years),
            'total_price': len(academic_years) * 1000  # Price per transcript
        }
        
        return redirect(url_for('student.request_summary'))
    
    # GET request - show form
    from simple_database_service import get_student_available_academic_years
    available_years = get_student_available_academic_years(session['user_id'])
    
    return render_template('student/request_transcript.html', 
                         available_years=available_years)

@student_bp.route('/request-summary')
@login_required
@student_required
def request_summary():
    """Request summary page"""
    if 'transcript_request' not in session:
        return redirect(url_for('student.request_transcript'))
    
    return render_template('student/request_summary.html', 
                         request=session['transcript_request'])

@student_bp.route('/payment', methods=['GET', 'POST'])
@login_required
@student_required
def payment():
    """Payment method selection"""
    if 'transcript_request' not in session:
        return redirect(url_for('student.request_transcript'))

    if request.method == 'POST':
        payment_method = request.form.get('payment_method')
        session['transcript_request']['payment_method'] = payment_method

        if payment_method == 'mobile_money':
            # Redirect to MoMo payment processing
            return redirect(url_for('student.momo_payment'))
        else:
            # Redirect to payment proof upload for other methods
            return redirect(url_for('student.payment_proof'))

    return render_template('student/payment.html',
                         request=session['transcript_request'])

@student_bp.route('/payment-proof', methods=['GET', 'POST'])
@login_required
@student_required
def payment_proof():
    """Payment proof upload"""
    if 'transcript_request' not in session:
        return redirect(url_for('student.request_transcript'))
    
    if request.method == 'POST':
        if 'payment_proof' not in request.files:
            flash('Please upload payment proof', 'error')
            return redirect(url_for('student.payment_proof'))
        
        file = request.files['payment_proof']
        if file.filename == '':
            flash('Please select a file', 'error')
            return redirect(url_for('student.payment_proof'))
        
        # Create request
        request_data = session.pop('transcript_request')
        success = create_transcript_request(session['user_id'], request_data, file)
        
        if success:
            flash('Request submitted successfully!', 'success')
        else:
            flash('Error submitting request', 'error')
        
        return redirect(url_for('student.dashboard'))
    
    return render_template('student/payment_proof.html')

@student_bp.route('/momo-payment', methods=['GET', 'POST'])
@login_required
@student_required
def momo_payment():
    """Mobile Money payment processing"""
    if 'transcript_request' not in session:
        return redirect(url_for('student.request_transcript'))

    if request.method == 'POST':
        phone_number = request.form.get('phone_number')

        if not phone_number:
            flash('Please enter your phone number', 'error')
            return redirect(url_for('student.momo_payment'))

        # Store phone number in session
        session['transcript_request']['phone_number'] = phone_number

        # Initiate MoMo payment
        from services.momo_api import initiate_payment

        # For testing, use 100 RWF instead of actual price
        amount = 100  # Test amount
        # amount = session['transcript_request']['total_price']  # Uncomment for production
        external_id = f"transcript_{session['user_id']}_{int(time.time())}"
        payer_message = f"INES Transcript Payment - {amount} RWF"

        payment_result = initiate_payment(
            phone_number=phone_number,
            amount=amount,
            external_id=external_id,
            payer_message=payer_message
        )

        if payment_result['success']:
            # Store transaction ID and payment details in session
            session['transcript_request']['transaction_id'] = payment_result['transaction_id']
            session['transcript_request']['phone_number'] = phone_number
            session['transcript_request']['payment_amount'] = amount
            session['transcript_request']['payment_initiated_at'] = datetime.now().isoformat()

            print(f"Payment initiated successfully. Transaction ID: {payment_result['transaction_id']}")
            # Redirect to payment status page
            return redirect(url_for('student.payment_status'))
        else:
            flash(f"Failed to initiate payment: {payment_result.get('message', 'Unknown error')}", 'error')
            return redirect(url_for('student.momo_payment'))

    return render_template('student/momo_payment.html',
                         request=session['transcript_request'])

@student_bp.route('/payment-status')
@login_required
@student_required
def payment_status():
    """Payment status monitoring page"""
    if 'transcript_request' not in session or 'transaction_id' not in session['transcript_request']:
        return redirect(url_for('student.request_transcript'))

    return render_template('student/payment_status.html',
                         transaction_id=session['transcript_request']['transaction_id'],
                         phone_number=session['transcript_request'].get('phone_number', ''))

@student_bp.route('/payment-complete')
@login_required
@student_required
def payment_complete():
    """Payment completion and request submission"""
    if 'transcript_request' not in session:
        return redirect(url_for('student.request_transcript'))

    # Create the transcript request automatically
    request_data = session.pop('transcript_request')

    # Import the create request function
    from simple_database_service import add_request

    try:
        new_request = add_request(
            student_reg_no=session['user_id'],
            academic_years=request_data['academic_years'],
            payment_method='mobile_money',
            total_price=request_data['total_price'],
            purpose='Academic Records',
            institution_name='INES-Ruhengeri',
            payment_proof_filename=f"momo_payment_{request_data.get('transaction_id', 'unknown')}.json"
        )

        if new_request:
            flash('Payment successful! Your transcript request has been submitted automatically.', 'success')
        else:
            flash('Payment successful, but there was an issue submitting your request. Please contact support.', 'warning')

    except Exception as e:
        flash(f'Payment successful, but there was an error processing your request: {str(e)}', 'error')

    return redirect(url_for('student.dashboard'))

@student_bp.route('/api/check-payment-status', methods=['GET', 'POST'])
@login_required
@student_required
def check_payment_status_api():
    """API endpoint to check payment status"""

    # Get transaction ID from session (for GET requests) or request data (for POST requests)
    if request.method == 'POST':
        data = request.get_json() or {}
        transaction_id = data.get('transaction_id')
    else:
        # GET request - get from session
        if 'transcript_request' not in session:
            return jsonify({'success': False, 'message': 'No payment session found'})
        transaction_id = session['transcript_request'].get('transaction_id')

    if not transaction_id:
        return jsonify({'success': False, 'message': 'Transaction ID not found'})

    # Check payment status using MoMo API
    from services.momo_api import momo_api
    from simple_database_service import update_payment_status_for_request, add_payment

    try:
        payment_result = momo_api.check_payment_status(transaction_id)

        if payment_result and payment_result.get('success'):
            status = payment_result.get('status')

            if status == 'successful':
                # Payment is successful - update database
                request_data = session.get('transcript_request', {})

                # Create the transcript request if it doesn't exist yet
                if 'request_id' not in request_data:
                    from simple_database_service import add_request

                    new_request = add_request(
                        student_reg_no=session['user_id'],
                        academic_years=request_data.get('academic_years', []),
                        payment_method='mobile_money',
                        total_price=request_data.get('total_price', 100),
                        purpose='Academic Records',
                        institution_name='INES-Ruhengeri',
                        payment_proof_filename=f"momo_payment_{transaction_id}.json"
                    )

                    if new_request:
                        request_data['request_id'] = new_request['id']
                        session['transcript_request'] = request_data

                        # Add payment record
                        add_payment(
                            student_reg_no=session['user_id'],
                            academic_year=','.join(request_data.get('academic_years', [])),
                            amount=request_data.get('total_price', 100),
                            payment_method='mobile_money',
                            request_id=new_request['id'],
                            department=session.get('department', 'Unknown')
                        )

                        # Update payment status to paid
                        update_payment_status_for_request(new_request['id'], 'paid', transaction_id)

                        return jsonify({
                            'success': True,
                            'status': 'successful',
                            'message': 'Payment completed successfully! Your transcript request has been submitted.',
                            'transaction_id': transaction_id
                        })
                    else:
                        return jsonify({
                            'success': False,
                            'message': 'Payment successful but failed to create transcript request. Please contact support.'
                        })
                else:
                    # Request already exists, just update payment status
                    update_payment_status_for_request(request_data['request_id'], 'paid', transaction_id)

                    return jsonify({
                        'success': True,
                        'status': 'successful',
                        'message': 'Payment completed successfully!',
                        'transaction_id': transaction_id
                    })

            elif status == 'failed':
                return jsonify({
                    'success': True,
                    'status': 'failed',
                    'message': payment_result.get('message', 'Payment failed'),
                    'transaction_id': transaction_id
                })
            else:  # pending
                return jsonify({
                    'success': True,
                    'status': 'pending',
                    'message': payment_result.get('message', 'Payment is still being processed'),
                    'transaction_id': transaction_id
                })
        else:
            return jsonify({
                'success': False,
                'message': payment_result.get('message', 'Failed to check payment status')
            })

    except Exception as e:
        print(f"Error in payment status check: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'message': f'Error checking payment status: {str(e)}'
        })

@student_bp.route('/request-status')
@login_required
@student_required
def request_status():
    """View request status"""
    requests = get_student_requests(session['user_id'])
    return render_template('student/request_status.html', requests=requests)

@student_bp.route('/view-downloads')
@login_required
@student_required
def view_downloads():
    """View completed downloads"""
    requests = get_student_requests(session['user_id'], status='completed')
    return render_template('student/view_downloads.html', 
                         approved_requests=requests)

@student_bp.route('/download/<int:request_id>')
@login_required
@student_required
def download_transcript(request_id):
    """Download transcript file"""
    return download_transcript_file(session['user_id'], request_id)

@student_bp.route('/api/dashboard-stats')
@login_required
@student_required
def dashboard_stats():
    """API endpoint for dashboard statistics"""
    student_id = session['user_id']
    stats = get_student_dashboard_data(student_id, stats_only=True)
    return jsonify(stats)